// components/WhyChooseUs.tsx
import Image from "next/image";
import "./WhyChoose.scss";
import { TableImage } from "@/public"; // Ensure this path is correct
import { Box, Typography } from "@mui/material";

export default function WhyChooseUs() {
  const tools = [
    { tool: "Wireshark", purpose: "Packet sniffing and protocol analysis." },
    { tool: "MQTT.fx / MQTT Explorer", purpose: "MQTT broker testing tools" },
    {
      tool: "LoRa WAN Network Server",
      purpose: "Test end devices in virtual networks.",
    },
    {
      tool: "Bluetooth Sniffers",
      purpose: "Analyze Bluetooth Low Energy (BLE) traffic.",
    },
    {
      tool: "Swagger / Postman",
      purpose: "Test APIs used in IoT backend communications.",
    },
    {
      tool: "WiFi/Cellular Signal Analyzers",
      purpose: "Analyze signal strength and connectivity.",
    },
    { tool: "Iperf / Ping", purpose: "Test network speed and latency." },
  ];

  return (
    <Box className="why-choose-us">
      <Box className="container">
        {/* Main Content Box */}
        <Box className="content-box">
          {/* Left Side - Bullet Points */}
          <Box className="bullet-points">
            <Box className="bullet-item">
              <Box className="bullet"></Box>
              <Typography className="bullet-text">
                Ensuring the product compliance's with global regulation bodies.
              </Typography>
            </Box>

            <Box className="bullet-item">
              <Box className="bullet"></Box>
              <Typography className="bullet-text">
                Stage wise end-to-end testing for the product from prototyping
                to production.
              </Typography>
            </Box>

            <Box className="bullet-item">
              <Box className="bullet"></Box>
              <Typography className="bullet-text">
                Comprehensive testing for EMI, EMC, safety, and environmental
                resilience.
              </Typography>
            </Box>

            <Box className="bullet-item">
              <Box className="bullet"></Box>
              <Typography className="bullet-text">
                Experienced team to handle complex embedded systems design
                needs.
              </Typography>
            </Box>
          </Box>

          {/* Right Side - Image */}
          <Box className="image-container">
            <Image src={TableImage} alt="Table Image" />
          </Box>
        </Box>

        {/* Table Title */}
        <Box className="table-title">
          <Typography className="table-title-text">
            Tools and Platforms for IoT Communication Testing
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}
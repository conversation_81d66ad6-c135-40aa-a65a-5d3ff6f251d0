.ai-vision-tek-page-container {
  .ai-vision-tek-page-content {
    .ai-vision-tek-header-image {
      width: 100%;
      max-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 50px;

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .ai-vision-tek-content {
      padding-left: 70px;
      padding-right: 70px;
      margin-bottom: 80px;

      .ai-vision-tek-title {
        margin-bottom: 33px;
        font-family: Poppins;
        font-weight: 400;
        font-size: 36px;

        line-height: 30px;
        letter-spacing: 0%;
        background: #8cffe4;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .ai-vision-tek-description-container {
        display: flex;
        flex-direction: column;
        gap: 40px;
        margin-bottom: 80px;

        .ai-vision-tek-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
        }
      }

      .ai-ml {
        margin-bottom: 50px;

        .ai-ml-title {
          font-family: Poppins;
          font-weight: 300;
          font-size: 34px;

          letter-spacing: 0%;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 54px;
        }

        .ai-ml-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
          margin-bottom: 40px;
        }

        .ai-ml-tech-stack {
          display: flex;
          gap: 139px;
          margin-left: 20px;

          ul {
            li {
              font-family: Poppins;
              font-weight: 400;
              font-size: 25px;

              line-height: 40px;
              letter-spacing: 0%;
              color: #ffffff;
            }
          }
        }
      }
    }

    .our-offerings {
      margin-bottom: 50px;
      /*  display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center; */

      .our-offerings-title {
        font-family: Poppins;
        font-weight: 400;
        font-size: 34px;
        letter-spacing: 0%;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 41px;
      }

      .our-offerings-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 34px;
      }

      .our-offerings-list {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 15px;
        width: 80%;
        margin-left: 10%;

        .offering-item-wrapper {
          display: flex;
          gap: 15px;
        }

        .offering-item {
          padding: 38.09px 27.41px 39.58px 34px;
          width: 100%;
          min-height: 280px;
          border: 1px solid #2499e280;
          color: #ffffff;

          .offering-item-title {
            font-family: Poppins;
            font-weight: 400;
            font-size: 22px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: center;
            background: #8cffe4;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 30.48px;
          }
        }
      }
    }

    .success-stories {
      margin-bottom: 98px;

      .success-stories-description {
        font-family: Poppins;
        font-weight: 500;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 30px;
      }

      .bullets-point {
        li {
          display: flex;
          flex-direction: row !important;
          align-items: start;
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #ffffff;
          display: flex;
          flex-direction: column;
          gap: 30px;
        }

        & li::before {
          content: "•";
          color: #ffffff;
          font-size: 20px;
          // margin-right: 10px;
        }
      }
    }

    // .case-study {
    //     padding-left: 93px;
    //     padding-right: 103px;
    //     margin-bottom: 301px;

    //     .case-study-title {
    //         font-family: Poppins;
    //         font-weight: 400;
    //         font-size: 34px;

    //         letter-spacing: 0%;
    //         text-align: center;
    //         background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
    //         background-clip: text;
    //         -webkit-text-fill-color: transparent;
    //         margin-bottom: 61px;
    //     }

    //     .case-study-images {
    //         display: flex;
    //         justify-content: center;
    //         align-items: center;
    //         gap: 59px;
    //         margin-bottom: 77.17px;

    //         img {
    //             width: 100%;
    //             max-width: 270px;
    //             min-height: 200px;
    //             border-radius: 10px;
    //         }

    //         .case-study-image-item {
    //             display: flex;
    //             flex-direction: column;
    //             align-items: center;
    //             gap: 44px;

    //             .image-title {
    //                 font-family: Montserrat;
    //                 font-weight: 400;
    //                 font-size: 24px;

    //                 line-height: 30px;
    //                 letter-spacing: 0%;
    //                 vertical-align: bottom;
    //                 color: #FFFFFF;
    //             }
    //         }
    //     }

    //     .case-study-description {
    //         .case-study-text {
    //             font-family: Poppins;
    //             font-weight: 400;
    //             font-style: italic;
    //             font-size: 16px;

    //             line-height: 30px;
    //             letter-spacing: 0%;
    //             color: #FFFFFF;
    //         }
    //     }
    // }

    .case-study {
      margin-top: 70px;
      padding-left: 93px;
      padding-right: 103px;
      margin-bottom: 150px;

      .case-study-title {
        font-family: Poppins;
        font-weight: 400;
        font-size: 34px;

        letter-spacing: 0%;
        text-align: center;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 61px;
      }

      .case-study-images {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 59px;
        margin-bottom: 77.17px;

        img {
          width: 100%;
          max-width: 270px;
          min-height: 200px;
          border-radius: 10px;
          transition: all 0.3s ease-in-out;
        }

        .case-study-image-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 44px;
          transition: all 0.3s ease-in-out;
          cursor: pointer;
          padding: 10px;
          border-radius: 12px;

          &:hover {
            transform: translateY(-10px);
            background: rgba(140, 255, 228, 0.05);

            img {
              transform: scale(1.05);
              box-shadow: 0 10px 20px rgba(140, 255, 228, 0.2);
            }

            .image-title {
              background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          img {
            transition: all 0.3s ease-in-out;
          }

          .image-title {
            font-family: Montserrat;
            font-weight: 400;
            font-size: 24px;

            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: bottom;
            color: #ffffff;
          }
        }
      }

      .case-study-description {
        .case-study-text {
          font-family: Poppins;
          font-weight: 400;
          font-style: italic;
          font-size: 16px;

          line-height: 30px;
          letter-spacing: 0%;
          color: #ffffff;
        }
      }
    }
  }
}

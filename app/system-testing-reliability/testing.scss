.testing-page-container {
    .testing-page-content {
        .testing-header-image {
            margin-bottom: 91px;

            img {
                width: 100%;
                height: auto;
                object-fit: cover;
            }
        }

        .testing-content {
            padding-left: 68px;
            padding-right: 72px;
            // margin-bottom: 100px;

            .testing-image-description {
                margin-bottom: 85px;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 20px;

                .testing-image-description-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;
                }
            }


            .our-specialization {
                .our-specialization-title {
                    font-family: Poppins;
                    font-weight: 275;
                    font-size: 48px;

                    letter-spacing: 0%;
                    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-bottom: 34px;
                }

                .our-specialization-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    vertical-align: middle;
                    color: #FFFFFF;
                    margin-bottom: 34px;
                }

                .tools-platforms {
                    margin-top: 75px;
                    margin-bottom: 93px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    img {
                        margin-bottom: 81px;
                    }

                    .tools-platforms-text {
                        font-family: Poppins;
                        font-weight: 600;
                        font-size: 30px;
                        line-height: 30px;
                        letter-spacing: 0%;
                        color: #FFFFFF;

                    }
                }

                .why-us {
                    .why-us-title {
                        font-family: Poppins;
                        font-weight: 275;
                        font-style: italic;
                        font-size: 34px;
                        letter-spacing: 0%;
                        background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                        background-clip: text;
                        -webkit-text-fill-color: transparent;
                        margin-bottom: 50px;
                        text-align: center;
                    }

                    .why-us-content {
                        display: flex;
                        flex-direction: column;
                        gap: 70px;
                        margin-bottom: 47px;

                        .why-us-points {
                            .why-us-list {
                                .why-us-item {
                                    font-family: Poppins;
                                    font-weight: 400;
                                    font-size: 20px;
                                    line-height: 34px;
                                    letter-spacing: 0%;
                                    text-align: left;
                                    color: #FFFFFF;
                                    margin-bottom: 20px;
                                }

                                & li::before {
                                    content: "•";
                                    color: #FFFFFF;
                                    font-size: 20px;
                                    margin-right: 20px;
                                }
                            }
                        }

                        .tools-table-container {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 30px;

                            .tools-table {
                                background: rgba(255, 255, 255, 0.1);
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                border-radius: 12px;
                                padding: 30px;
                                width: 100%;
                                max-width: 900px;

                                .tools-table-header {
                                    display: grid;
                                    grid-template-columns: 1fr 2fr;
                                    gap: 30px;
                                    padding-bottom: 20px;
                                    border-bottom: 2px solid #8CFFE4;
                                    margin-bottom: 20px;

                                    .table-header-tool,
                                    .table-header-purpose {
                                        font-family: Poppins;
                                        font-weight: 600;
                                        font-size: 18px;
                                        color: #8CFFE4;
                                        text-align: left;
                                    }
                                }

                                .tools-table-row {
                                    display: grid;
                                    grid-template-columns: 1fr 2fr;
                                    gap: 30px;
                                    padding: 15px 0;
                                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

                                    &:last-child {
                                        border-bottom: none;
                                    }

                                    .table-cell-tool {
                                        font-family: Poppins;
                                        font-weight: 500;
                                        font-size: 16px;
                                        color: #FFFFFF;
                                        text-align: left;
                                    }

                                    .table-cell-purpose {
                                        font-family: Poppins;
                                        font-weight: 400;
                                        font-size: 16px;
                                        color: #FFFFFF;
                                        text-align: left;
                                        line-height: 24px;
                                    }
                                }
                            }

                            .tools-table-title {
                                font-family: Poppins;
                                font-weight: 600;
                                font-size: 24px;
                                color: #FFFFFF;
                                text-align: center;
                                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                                background-clip: text;
                                -webkit-text-fill-color: transparent;
                            }
                        }
                    }

                    .caption {
                        .caption-text {
                            font-family: Poppins;
                            font-weight: 400;
                            font-style: italic;
                            font-size: 20px;
                            line-height: 30px;
                            letter-spacing: 0%;
                            text-align: center;
                            vertical-align: middle;
                            color: #FFFFFF;
                        }
                    }
                }
            }
        }


    }
}
.testing-page-container {
  .testing-page-content {
    .testing-header-image {
      margin-bottom: 91px;

      img {
        width: 100%;
        height: auto;
        object-fit: cover;
      }
    }

    .testing-content {
      padding-left: 68px;
      padding-right: 72px;
      // margin-bottom: 100px;

      .testing-image-description {
        margin-bottom: 85px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;

        .testing-image-description-text {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
        }
      }

      .our-specialization {
        .our-specialization-title {
          font-family: Poppins;
          font-weight: 275;
          font-size: 48px;

          letter-spacing: 0%;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 34px;
        }

        .our-specialization-text {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          color: #ffffff;
          margin-bottom: 34px;
        }

        .tools-platforms {
          margin-top: 75px;
          margin-bottom: 93px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          img {
            margin-bottom: 81px;
          }

          .tools-platforms-text {
            font-family: Poppins;
            font-weight: 600;
            font-size: 30px;
            line-height: 30px;
            letter-spacing: 0%;
            color: #ffffff;
          }
        }

        .why-us {
          .why-us-title {
            font-family: Poppins;
            font-weight: 275;
            font-style: italic;
            font-size: 34px;

            letter-spacing: 0%;
            background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 30px;
          }

          .why-us-list {
            margin-bottom: 47px;

            .why-us-item {
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;

              line-height: 34px;
              letter-spacing: 0%;
              text-align: justify;
              color: #ffffff;
            }

            & li::before {
              content: "•";
              color: #ffffff;
              font-size: 20px;
              margin-right: 20px;
            }
          }

          .caption {
            .caption-text {
              font-family: Poppins;
              font-weight: 400;
              font-style: italic;
              font-size: 20px;

              line-height: 30px;
              letter-spacing: 0%;
              text-align: center;
              vertical-align: middle;
              color: #ffffff;
            }
          }
        }
      }
    }
    .why-choose-us {
      margin-top: 119px;
      margin-bottom: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}

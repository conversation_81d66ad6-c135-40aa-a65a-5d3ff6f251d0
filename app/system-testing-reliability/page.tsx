import "./testing.scss";
import { Box, List, ListItem, Typography } from "@mui/material";
import Image from "next/image";
import { system_testing_img1, system_testing_img2 } from "@/public/index";
import TestingAccordions from "@/components/system-testing/TestingAccordions";

const SystemTestingpage = () => {
  return (
    <Box className="testing-page-container">
      <Box className="testing-page-content">
        <Box className="testing-header-image" data-aos="fade-down">
          <Image src={system_testing_img1} alt="IOT Image 1" />
        </Box>

        <Box
          className="testing-content"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          <Box className="testing-image-description">
            <Typography
              className="testing-image-description-text"
              data-aos="fade-up"
              data-aos-delay="100"
            >
              Aadvik TekLabs offers a comprehensive testing services for
              embedded systems, including system function testing, product
              reliability assessments, and compliance testing to ensure
              adherence with global standards such as ISO, IEC, UL, and CE and
              ensure that system design should meet the highest standards of
              safety, reliability, and regulatory compliance.
            </Typography>

            <Typography
              className="testing-image-description-text"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              To ensure optimal performance and safety, our team diligently
              performs comprehensive electrical and functional testing before
              each product leaves our facility. Our quality assurance team
              thoroughly verifies every product across all key parameters.
            </Typography>
          </Box>

          <Box className="our-specialization">
            <Typography
              className="our-specialization-title"
              data-aos="fade-up"
              data-aos-delay="300"
            >
              Our Specialization
            </Typography>

            <Typography
              className="our-specialization-text"
              data-aos="fade-up"
              data-aos-delay="400"
            >
              Aadvik Engineers are specialized support in reliability
              engineering and testing compliance with FCC, RoHS, and safety
              regulations, ensuring your product meets critical regulatory
              requirements and industry standards. We are specialize in:
            </Typography>

            <TestingAccordions />

            <Box className="tools-platforms">
              <Image
                src={system_testing_img2}
                alt="Testing Tools and Platforms"
                className="tools-platforms-image"
                data-aos="fade-up"
                data-aos-delay="500"
              />
              <Typography
                className="tools-platforms-text"
                data-aos="fade-up"
                data-aos-delay="600"
              >
                Tools and Platforms for IoT Communication Testing
              </Typography>
            </Box>

            <Box className="why-us" data-aos="fade-up" data-aos-delay="300">
              <Typography className="why-us-title">
                Why to choose Us?
              </Typography>

              <Box className="why-us-content">
                <Box className="why-us-points">
                  <List className="why-us-list">
                    <ListItem className="why-us-item">
                      Ensuring the product compliance's with global regulation bodies.
                    </ListItem>
                    <ListItem className="why-us-item">
                      Stage wise end-to-end testing for the product from prototyping to production.
                    </ListItem>
                    <ListItem className="why-us-item">
                      Comprehensive testing for EMI, EMC, safety, and environmental resilience.
                    </ListItem>
                    <ListItem className="why-us-item">
                      Experienced team to handle complex embedded systems design needs.
                    </ListItem>
                  </List>
                </Box>

                <Box className="tools-table-container">
                  <Box className="tools-table">
                    <Box className="tools-table-header">
                      <Typography className="table-header-tool">Tool/ Platform</Typography>
                      <Typography className="table-header-purpose">Purpose</Typography>
                    </Box>

                    <Box className="tools-table-row">
                      <Typography className="table-cell-tool">Wireshark</Typography>
                      <Typography className="table-cell-purpose">Packet sniffing and protocol analysis.</Typography>
                    </Box>

                    <Box className="tools-table-row">
                      <Typography className="table-cell-tool">MQTT Fx / MQTT Explorer</Typography>
                      <Typography className="table-cell-purpose">MQTT broker testing tools</Typography>
                    </Box>

                    <Box className="tools-table-row">
                      <Typography className="table-cell-tool">LoRa WAN Network Server</Typography>
                      <Typography className="table-cell-purpose">Test end devices in virtual networks.</Typography>
                    </Box>

                    <Box className="tools-table-row">
                      <Typography className="table-cell-tool">Bluetooth Sniffers</Typography>
                      <Typography className="table-cell-purpose">Analyze Bluetooth Low Energy (BLE) traffic.</Typography>
                    </Box>

                    <Box className="tools-table-row">
                      <Typography className="table-cell-tool">Swagger / Postman</Typography>
                      <Typography className="table-cell-purpose">Test APIs used in IoT backend communications.</Typography>
                    </Box>

                    <Box className="tools-table-row">
                      <Typography className="table-cell-tool">WiFi/Cellular Signal Analyzers</Typography>
                      <Typography className="table-cell-purpose">Analyze signal strength and connectivity.</Typography>
                    </Box>

                    <Box className="tools-table-row">
                      <Typography className="table-cell-tool">Iperf / Ping</Typography>
                      <Typography className="table-cell-purpose">Test network speed and latency.</Typography>
                    </Box>
                  </Box>

                  <Typography className="tools-table-title">
                    Tools and Platforms for IoT Communication Testing
                  </Typography>
                </Box>
              </Box>

              <Box className="caption" data-aos="fade-up" data-aos-delay="300">
                <Typography className="caption-text">
                  Aadvik TekLabs provides comprehensive hardware and software
                  testing services, ensuring products meet industry standards,
                  deliver top performance, ensure user safety, and are
                  market-readiness of product.
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SystemTestingpage;

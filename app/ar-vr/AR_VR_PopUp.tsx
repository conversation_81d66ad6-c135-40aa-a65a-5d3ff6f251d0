import React from "react";
import { Dialog, DialogContent, IconButton, Box } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import Image, { StaticImageData } from "next/image";

interface AR_VR_PopUpProps {
  open: boolean;
  onClose: () => void;
  image: StaticImageData | string;
  alt?: string;
}

const AR_VR_PopUp: React.FC<AR_VR_PopUpProps> = ({
  open,
  onClose,
  image,
  alt = "AR VR Image",
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: "transparent",
          boxShadow: "none",
          overflow: "hidden",
        },
      }}
    >
      <DialogContent sx={{ padding: 0, position: "relative" }}>
        <IconButton
          onClick={onClose}
          sx={{
            position: "absolute",
            right: 20,
            top: 70,
            color: "#fff",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.7)",
            },
            zIndex: 1,
          }}
        >
          <CloseIcon />
        </IconButton>
        <Box
          sx={{
            position: "relative",
            width: "100%",
            height: "80vh",
          }}
        >
          <Image
            src={image}
            alt={alt}
            fill
            style={{
              objectFit: "contain",
            }}
            quality={100}
          />
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AR_VR_PopUp;
